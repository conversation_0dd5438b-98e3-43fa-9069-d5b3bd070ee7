package main

import (
	"database/sql"
	"errors"
	"github.com/olzzhas/kazakh-lingo/internal/data"
	"net/http"
)

func (app *application) checkProgressHandler(w http.ResponseWriter, r *http.Request) {

}

func (app *application) saveProgressHandler(w http.ResponseWriter, r *http.Request) {
	var input struct {
		UserId              int    `json:"user_id"`
		ModuleId            int    `json:"module_id"`
		MistakenQuestionIds []int  `json:"mistaken_question_ids"`
		Time                string `json:"time"`
	}

	err := app.readJSON(w, r, &input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	progress := &data.Progress{
		UserID:              input.UserId,
		ModuleID:            input.ModuleId,
		MistakenQuestionIds: input.MistakenQuestionIds,
		Time:                input.Time,
	}

	progress, err = app.models.Progress.SaveProgress(progress)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusCreated, envelope{"progress": progress}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) checkStreakHandler(w http.ResponseWriter, r *http.Request) {
	userID, err := app.readIDParam(r)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	streak, err := app.models.Progress.GetStreak(userID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			app.notFoundResponse(w, r)
			return
		}
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"streak": streak}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}
