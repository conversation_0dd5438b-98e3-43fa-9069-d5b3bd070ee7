package data

import (
	"database/sql"
	"errors"
	"github.com/go-redis/redis/v8"
)

type Sentence struct {
	ID           int    `json:"id"`
	KazPlaintext string `json:"kaz_plaintext"`
	RusPlaintext string `json:"rus_plaintext"`
	AudioURL     string `json:"audio_url"`
}

type SentenceModel struct {
	DB    *sql.DB
	Redis *redis.Client
}

// Insert Создание предложения
func (m *SentenceModel) Insert(sentence *Sentence) error {
	query := `
		INSERT INTO sentences (kaz_plaintext, rus_plaintext, audio_url)
		VALUES ($1, $2, $3)
		RETURNING id
	`
	args := []any{sentence.KazPlaintext, sentence.RusPlaintext, sentence.AudioURL}

	return m.DB.QueryRow(query, args...).Scan(&sentence.ID)
}

// GetAll Получение всех предложений
func (m *SentenceModel) GetAll() ([]Sentence, error) {
	query := `SELECT id, kaz_plaintext, rus_plaintext, audio_url FROM sentences`
	rows, err := m.DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var sentences []Sentence
	for rows.Next() {
		var sentence Sentence
		err := rows.Scan(&sentence.ID, &sentence.KazPlaintext, &sentence.RusPlaintext, &sentence.AudioURL)
		if err != nil {
			return nil, err
		}
		sentences = append(sentences, sentence)
	}

	return sentences, nil
}

func (m *SentenceModel) Get(id int) (*Sentence, error) {
	query := `SELECT id, kaz_plaintext, rus_plaintext, audio_url FROM sentences WHERE id = $1`
	row := m.DB.QueryRow(query, id)

	var sentence Sentence
	err := row.Scan(&sentence.ID, &sentence.KazPlaintext, &sentence.RusPlaintext, &sentence.AudioURL)
	if errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	return &sentence, nil
}
