package main

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/olzzhas/kazakh-lingo/internal/storage"
)

// uploadAudioHandler обрабатывает загрузку аудио файлов
func (app *application) uploadAudioHandler(w http.ResponseWriter, r *http.Request) {
	// Ограничиваем размер файла до 10MB
	r.ParseMultipartForm(10 << 20)

	file, handler, err := r.FormFile("audio")
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}
	defer file.Close()

	// Проверяем тип файла
	if !isValidAudioFile(handler.Filename) {
		app.badRequestResponse(w, r, fmt.Errorf("invalid audio file type. Allowed: mp3, wav, ogg, m4a"))
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Загружаем файл в MinIO
	fileURL, err := app.storages.MinIO.UploadAudio(ctx, file, handler)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	response := envelope{
		"message":  "Audio file uploaded successfully",
		"file_url": fileURL,
	}

	err = app.writeJSON(w, http.StatusCreated, response, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
	}
}

// uploadImageHandler обрабатывает загрузку изображений
func (app *application) uploadImageHandler(w http.ResponseWriter, r *http.Request) {
	// Ограничиваем размер файла до 5MB
	r.ParseMultipartForm(5 << 20)

	file, handler, err := r.FormFile("image")
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}
	defer file.Close()

	// Проверяем тип файла
	if !isValidImageFile(handler.Filename) {
		app.badRequestResponse(w, r, fmt.Errorf("invalid image file type. Allowed: jpg, jpeg, png, gif, webp"))
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Загружаем файл в MinIO
	fileURL, err := app.storages.MinIO.UploadImage(ctx, file, handler)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	response := envelope{
		"message":  "Image file uploaded successfully",
		"file_url": fileURL,
	}

	err = app.writeJSON(w, http.StatusCreated, response, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
	}
}

// deleteFileHandler удаляет файл из хранилища
func (app *application) deleteFileHandler(w http.ResponseWriter, r *http.Request) {
	var input struct {
		FileURL string `json:"file_url"`
	}

	err := app.readJSON(w, r, &input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	if input.FileURL == "" {
		app.badRequestResponse(w, r, fmt.Errorf("file_url is required"))
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Удаляем файл из MinIO
	err = app.storages.MinIO.DeleteFile(ctx, input.FileURL)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	response := envelope{
		"message": "File deleted successfully",
	}

	err = app.writeJSON(w, http.StatusOK, response, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
	}
}

// listFilesHandler возвращает список файлов в bucket
func (app *application) listFilesHandler(w http.ResponseWriter, r *http.Request) {
	bucketName := r.URL.Query().Get("bucket")
	prefix := r.URL.Query().Get("prefix")

	if bucketName == "" {
		app.badRequestResponse(w, r, fmt.Errorf("bucket parameter is required"))
		return
	}

	// Проверяем, что bucket разрешен
	if bucketName != "klingo-audio" && bucketName != "klingo-images" {
		app.badRequestResponse(w, r, fmt.Errorf("invalid bucket name"))
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	files, err := app.storages.MinIO.ListFiles(ctx, bucketName, prefix)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	response := envelope{
		"bucket": bucketName,
		"prefix": prefix,
		"files":  files,
		"count":  len(files),
	}

	err = app.writeJSON(w, http.StatusOK, response, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
	}
}

// uploadMultipleFilesHandler обрабатывает загрузку нескольких файлов
func (app *application) uploadMultipleFilesHandler(w http.ResponseWriter, r *http.Request) {
	// Ограничиваем размер до 50MB для множественной загрузки
	r.ParseMultipartForm(50 << 20)

	files := r.MultipartForm.File["files"]
	if len(files) == 0 {
		app.badRequestResponse(w, r, fmt.Errorf("no files provided"))
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	var uploadedFiles []map[string]string
	var errors []string

	for _, fileHeader := range files {
		file, err := fileHeader.Open()
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to open file %s: %v", fileHeader.Filename, err))
			continue
		}

		var fileURL string
		var fileType storage.FileType

		// Определяем тип файла
		if isValidAudioFile(fileHeader.Filename) {
			fileType = storage.FileTypeAudio
		} else if isValidImageFile(fileHeader.Filename) {
			fileType = storage.FileTypeImage
		} else {
			file.Close()
			errors = append(errors, fmt.Sprintf("Invalid file type for %s", fileHeader.Filename))
			continue
		}

		// Загружаем файл
		fileURL, err = app.storages.MinIO.UploadFile(ctx, file, fileHeader, fileType)
		file.Close()

		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to upload %s: %v", fileHeader.Filename, err))
			continue
		}

		uploadedFiles = append(uploadedFiles, map[string]string{
			"filename": fileHeader.Filename,
			"file_url": fileURL,
			"type":     string(fileType),
		})
	}

	response := envelope{
		"message":        "Multiple file upload completed",
		"uploaded_files": uploadedFiles,
		"uploaded_count": len(uploadedFiles),
		"total_count":    len(files),
	}

	if len(errors) > 0 {
		response["errors"] = errors
		response["error_count"] = len(errors)
	}

	statusCode := http.StatusCreated
	if len(errors) > 0 && len(uploadedFiles) == 0 {
		statusCode = http.StatusBadRequest
	} else if len(errors) > 0 {
		statusCode = http.StatusPartialContent
	}

	err := app.writeJSON(w, statusCode, response, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
	}
}

// isValidAudioFile проверяет, является ли файл допустимым аудио файлом
func isValidAudioFile(filename string) bool {
	filename = strings.ToLower(filename)
	validExtensions := []string{".mp3", ".wav", ".ogg", ".m4a"}
	
	for _, ext := range validExtensions {
		if strings.HasSuffix(filename, ext) {
			return true
		}
	}
	return false
}

// isValidImageFile проверяет, является ли файл допустимым изображением
func isValidImageFile(filename string) bool {
	filename = strings.ToLower(filename)
	validExtensions := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}
	
	for _, ext := range validExtensions {
		if strings.HasSuffix(filename, ext) {
			return true
		}
	}
	return false
}
