services:
   # База данных Postgres
   postgres:
      image: postgres:14
      environment:
         POSTGRES_DB: klingo_dev
         POSTGRES_USER: olzzhas
         POSTGRES_PASSWORD: Olzhas040404
      volumes:
         - postgres_data:/var/lib/postgresql/data
      ports:
         - "5432:5432"
      healthcheck:
         test: ["CMD-SHELL", "pg_isready -U olzzhas -d klingo_dev"]
         interval: 10s
         timeout: 5s
         retries: 5
      networks:
         - dev

   # Redis для кэширования
   redis:
      image: redis:alpine
      ports:
         - "6379:6379"
      healthcheck:
         test: ["CMD", "redis-cli", "ping"]
         interval: 10s
         timeout: 5s
         retries: 3
      volumes:
         - redis_data:/data
      networks:
         - dev

   mongo:
      image: mongo:5
      environment:
         MONGO_INITDB_ROOT_USERNAME: olzzhas
         MONGO_INITDB_ROOT_PASSWORD: Olzhas040404
      ports:
         - "27017:27017"
      networks:
         - dev

      # Миграции базы данных
   migrate:
      image: migrate/migrate:v4.15.2
      volumes:
         - ./migrations:/migrations
      entrypoint:
         [
            "/bin/sh",
            "-c",
            "sleep 10 && migrate -path /migrations -database *********************************************/klingo_dev?sslmode=disable up",
         ]
      depends_on:
         postgres:
            condition: service_healthy
      networks:
         - dev

   # Бэкенд-приложение
   app:
      build:
         context: .
         dockerfile: Dockerfile
      depends_on:
         - postgres
         - redis
         - migrate
         - mongo # Зависимость от MongoDB для логов
         - minio # Зависимость от MinIO для файлов
      environment:
         DB_DSN: "*********************************************/klingo_dev?sslmode=disable"
         REDIS_URL: "redis://redis:6379"
         MONGO_URL: "************************************************"
         MINIO_ENDPOINT: "minio:9000"
         MINIO_ACCESS_KEY: "olzzhas"
         MINIO_SECRET_KEY: "Olzhas040404"
         MINIO_USE_SSL: "false"
         PORT: 4000
      ports:
         - "8080:4000" # Порт для бэкенда
      command: ["sh", "-c", "sleep 10 && ./sport-hub"]
      env_file:
         - .env
      networks:
         - dev

   # Nginx для маршрутизации запросов
   nginx:
      image: nginx:stable-alpine
      ports:
         - "80:80"
         - "443:443"
      volumes:
         - "./nginx.conf:/etc/nginx/nginx.conf"
         - "/etc/letsencrypt/live/narxoz-klingo.online/fullchain.pem:/etc/nginx/ssl/fullchain.pem:ro"
         - "/etc/letsencrypt/live/narxoz-klingo.online/privkey.pem:/etc/nginx/ssl/privkey.pem:ro"
      depends_on:
         - app
      networks:
         - dev

   rabbitmq:
      image: rabbitmq:management
      environment:
         RABBITMQ_DEFAULT_USER: guest
         RABBITMQ_DEFAULT_PASS: guest
      ports:
         - "5672:5672" # Порт для AMQP (RabbitMQ протокол)
         - "15672:15672" # Порт для веб-интерфейса управления RabbitMQ
      networks:
         - dev

   # MinIO для хранения файлов (аудио, изображения)
   minio:
      image: minio/minio:latest
      environment:
         MINIO_ROOT_USER: olzzhas
         MINIO_ROOT_PASSWORD: Olzhas040404
      ports:
         - "9000:9000" # API порт
         - "9001:9001" # Console порт
      volumes:
         - minio_data:/data
      command: server /data --console-address ":9001"
      healthcheck:
         test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
         interval: 30s
         timeout: 20s
         retries: 3
      networks:
         - dev

volumes:
   postgres_data:
   redis_data:
   mongo_data:
   minio_data:

networks:
   dev:
      driver: bridge
