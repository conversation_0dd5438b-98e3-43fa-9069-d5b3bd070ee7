package data

import (
	"database/sql"
	"errors"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/mongo"
)

var (
	ErrRecordNotFound = errors.New("record not found")
	ErrEditConflict   = errors.New("edit conflict")
)

type Models struct {
	Users               UserModel
	AuthorizationTokens AuthorizationTokenModel
	Questions           QuestionModel
	Words               WordModel
	Sentences           SentenceModel
	Theories            TheoryModel
	Modules             ModuleModel
	Progress            ProgressModel
	Achievements        AchievementModel
}

func NewModels(db *sql.DB, redis *redis.Client, mongoClient *mongo.Client) Models {
	return Models{
		Users:               UserModel{DB: db, Redis: redis, Mongo: mongoClient},
		AuthorizationTokens: AuthorizationTokenModel{DB: db, Redis: redis},
		Questions:           QuestionModel{DB: db, Redis: redis},
		Words:               WordModel{DB: db, Redis: redis},
		Sentences:           SentenceModel{DB: db, Redis: redis},
		Theories:            TheoryModel{DB: db, Redis: redis},
		Modules:             ModuleModel{DB: db, Redis: redis},
		Progress:            ProgressModel{DB: db, Redis: redis},
		Achievements:        AchievementModel{DB: db, Redis: redis},
	}
}
