package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"expvar"
	"flag"
	"log"
	"os"
	"runtime"
	"strconv"
	"sync"
	"time"

	"cloud.google.com/go/storage"
	"github.com/go-redis/redis/v8"
	"github.com/joho/godotenv"
	_ "github.com/lib/pq"
	"github.com/olzzhas/kazakh-lingo/internal/data"
	"github.com/olzzhas/kazakh-lingo/internal/jsonlog"
	minioStorage "github.com/olzzhas/kazakh-lingo/internal/storage"
	"github.com/streadway/amqp"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/writeconcern"
)

const version = "1.0.0"

type config struct {
	port int
	env  string
	db   struct {
		dsn          string
		maxOpenConns int
		maxIdleConns int
		maxIdleTime  string
	}

	limiter struct {
		rps     float64
		burst   int
		enabled bool
	}

	smtp struct {
		host     string
		port     int
		username string
		password string
		sender   string
	}
}

type application struct {
	config   config
	logger   *jsonlog.Logger
	models   data.Models
	redis    *redis.Client
	storages data.Storages
	//mailer      mailer.Mailer
	wg          sync.WaitGroup
	rabbitMQ    *amqp.Connection
	mongoClient *mongo.Client
}

func main() {
	var cfg config

	err := godotenv.Load()
	if err != nil {
		log.Fatal("Error loading .env file")
	}

	PORT, _ := strconv.Atoi(os.Getenv("PORT"))
	if PORT < 1 {
		PORT = 4000
	}

	var env string
	if os.Getenv("ENV") == "" {
		env = "production"
	}

	flag.IntVar(&cfg.port, "port", PORT, "API server port")
	flag.StringVar(&cfg.env, "env", env, "development|staging|production")
	flag.StringVar(&cfg.db.dsn, "db-dsn", os.Getenv("DB_DSN"), "PostgreSQL DSN")
	flag.IntVar(&cfg.db.maxOpenConns, "db-max-open-conns", 2000, "PostgreSQL max open connections")
	flag.IntVar(&cfg.db.maxIdleConns, "db-max-idle-conns", 100, "PostgreSQL max idle connections")
	flag.StringVar(&cfg.db.maxIdleTime, "db-max-idle-time", "15m", "PostgreSQL max connection idle time")
	flag.Float64Var(&cfg.limiter.rps, "limiter-rps", 10000, "Rate limiter maximum requests per second")
	flag.IntVar(&cfg.limiter.burst, "limiter-burst", 50000, "Rate limiter maximum burst")
	flag.BoolVar(&cfg.limiter.enabled, "limiter-enabled", true, "Enable rate limiter")

	smtpPort, _ := strconv.Atoi(os.Getenv("SMTP_PORT"))
	flag.StringVar(&cfg.smtp.host, "smtp-host", os.Getenv("SMTP_HOST"), "SMTP host")
	flag.IntVar(&cfg.smtp.port, "smtp-port", smtpPort, "SMTP port")
	flag.StringVar(&cfg.smtp.username, "smtp-username", os.Getenv("SMTP_USERNAME"), "SMTP username")
	flag.StringVar(&cfg.smtp.password, "smtp-password", os.Getenv("SMTP_PASSWORD"), "SMTP password")
	flag.StringVar(&cfg.smtp.sender, "smtp-sender", os.Getenv("SMTP_SENDER"), "SMTP sender")

	flag.Parse()

	// logger
	logger, err := jsonlog.New(os.Stdout, jsonlog.LevelInfo, "amqp://guest:guest@rabbitmq:5672/")
	if err != nil {
		log.Fatal("Failed to connect to RabbitMQ:", err)
	}

	// Подключение к Redis
	redisClient, err := redisConnect(cfg.env)
	if err != nil {
		logger.PrintFatal(err, nil, "general")
	}
	logger.PrintInfo("redis connection established", nil, "general")

	// Подключение к Google Storage
	ctx := context.Background()
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	storageClient, err := storage.NewClient(ctx)
	if err != nil {
		log.Fatalf("failed to create Google Storage client: %v", err)
	}
	logger.PrintInfo("google storage connection established", nil, "general")

	defer func(storageClient *storage.Client) {
		err := storageClient.Close()
		if err != nil {
			logger.PrintFatal(err, nil, "general")
		}
	}(storageClient)

	// Подключение к MinIO
	minioClient, err := connectMinIO()
	if err != nil {
		logger.PrintFatal(err, nil, "general")
	}
	logger.PrintInfo("minio connection established", nil, "general")

	// Подключение к PostgreSQL
	db, err := openDB(cfg)
	if err != nil {
		logger.PrintFatal(err, nil, "general")
	}
	defer func(db *sql.DB) {
		err := db.Close()
		if err != nil {
			logger.PrintFatal(err, nil, "general")
		}
	}(db)
	logger.PrintInfo("database connection pool established", nil, "general")

	// Подключение к MongoDB
	mongoClient, err := connectMongoDB()
	if err != nil {
		logger.PrintFatal(err, nil, "general")
	}
	logger.PrintInfo("mongo connected successfully", nil, "general")

	// Подключение к RabbitMQ
	rabbitConn, err := connectRabbitMQ()
	if err != nil {
		logger.PrintFatal(err, nil, "general")
	}
	defer rabbitConn.Close()
	logger.PrintInfo("rabbitmq connection established", nil, "general")

	// Добавление переменных для мониторинга
	expvar.NewString("version").Set(version)

	// Публикация количества активных горутин
	expvar.Publish("goroutines", expvar.Func(func() any {
		return runtime.NumGoroutine()
	}))

	// Публикация статистики подключения к базе данных
	expvar.Publish("database", expvar.Func(func() any {
		return db.Stats()
	}))

	// Публикация текущего времени в виде Unix timestamp
	expvar.Publish("timestamp", expvar.Func(func() any {
		return time.Now().Unix()
	}))

	app := &application{
		config:   cfg,
		logger:   logger,
		models:   data.NewModels(db, redisClient, mongoClient),
		storages: data.NewStorages(storageClient, minioClient),
		redis:    redisClient,
		//mailer:      mailer.New(cfg.smtp.host, cfg.smtp.port, cfg.smtp.username, cfg.smtp.password, cfg.smtp.sender),
		rabbitMQ:    rabbitConn,
		mongoClient: mongoClient,
	}

	go app.consumeLogsFromRabbitMQ()

	err = app.serve()
	if err != nil {
		logger.PrintFatal(err, nil, "general")
	}
}

// Подключение к PostgreSQL
func openDB(cfg config) (*sql.DB, error) {
	db, err := sql.Open("postgres", cfg.db.dsn)
	if err != nil {
		return nil, err
	}
	db.SetMaxOpenConns(cfg.db.maxOpenConns)
	db.SetMaxIdleConns(cfg.db.maxIdleConns)
	duration, err := time.ParseDuration(cfg.db.maxIdleTime)
	if err != nil {
		return nil, err
	}
	db.SetConnMaxIdleTime(duration)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	err = db.PingContext(ctx)
	if err != nil {
		return nil, err
	}
	return db, nil
}

// Подключение к Redis
func redisConnect(env string) (*redis.Client, error) {
	redisClient := redis.NewClient(&redis.Options{
		Addr:     "redis:6379",
		Password: "",
		DB:       0,
	})
	if err := redisClient.Ping(context.Background()).Err(); err != nil {
		return nil, err
	}
	return redisClient, nil
}

// Подключение к MongoDB
func connectMongoDB() (*mongo.Client, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	clientOptions := options.Client().SetAuth(options.Credential{
		Username:      "olzzhas",
		Password:      "Olzhas040404",
		AuthMechanism: "SCRAM-SHA-256",
	}).ApplyURI("mongodb://mongo:27017/kling_logs")

	clientOptions.SetWriteConcern(writeconcern.New(writeconcern.WMajority()))
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return nil, err
	}
	err = client.Ping(ctx, nil)
	if err != nil {
		return nil, err
	}
	return client, nil
}

// Подключение к RabbitMQ
func connectRabbitMQ() (*amqp.Connection, error) {
	conn, err := amqp.Dial("amqp://guest:guest@rabbitmq:5672/")
	if err != nil {
		return nil, err
	}
	return conn, nil
}

// Подключение к MinIO
func connectMinIO() (*minioStorage.MinIOStorage, error) {
	endpoint := os.Getenv("MINIO_ENDPOINT")
	accessKey := os.Getenv("MINIO_ACCESS_KEY")
	secretKey := os.Getenv("MINIO_SECRET_KEY")
	useSSL := os.Getenv("MINIO_USE_SSL") == "true"

	if endpoint == "" {
		endpoint = "localhost:9000"
	}
	if accessKey == "" {
		accessKey = "olzzhas"
	}
	if secretKey == "" {
		secretKey = "Olzhas040404"
	}

	minioClient, err := minioStorage.NewMinIOStorage(endpoint, accessKey, secretKey, useSSL)
	if err != nil {
		return nil, err
	}

	return minioClient, nil
}

func (app *application) consumeLogsFromRabbitMQ() {
	ch, err := app.rabbitMQ.Channel()
	if err != nil {
		app.logger.PrintFatal(err, nil, "general")
	}
	defer ch.Close()

	msgs, err := ch.Consume(
		"logs", // Имя очереди
		"",     // Consumer tag
		true,   // Auto-ack
		false,  // Exclusive
		false,  // No-local
		false,  // No-wait
		nil,    // Arguments
	)
	if err != nil {
		app.logger.PrintFatal(err, nil, "general")
	}

	for msg := range msgs {
		var logMessage struct {
			Level      string         `json:"level"`
			Time       string         `json:"time"`
			Message    string         `json:"message"`
			Properties map[string]any `json:"properties,omitempty"`
			Collection string         `json:"collection"`
		}

		err := json.Unmarshal(msg.Body, &logMessage)
		if err != nil {
			app.logger.PrintError(err, nil, "general")
			continue
		}

		// Динамически выбираем коллекцию
		mongoCollection := app.mongoClient.Database("klingo_logs").Collection(logMessage.Collection)

		_, err = mongoCollection.InsertOne(context.TODO(), logMessage)
		if err != nil {
			app.logger.PrintError(err, nil, "general")
		}
	}
}
