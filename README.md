# Kazakh-Lingo 🇰🇿

Платформа для изучения казахского языка с современной архитектурой на Go.

## 🚀 Быстрый старт

### Предварительные требования

- Docker и Docker Compose
- Go 1.22+ (для разработки)
- PostgreSQL 14+ (опционально, для локальной разработки)

### Запуск проекта

1. **Клонирование репозитория**
```bash
git clone <repository-url>
cd kazakh-lingo
```

2. **Создание .env файла**
```bash
cp .env.example .env
# Отредактируйте переменные окружения при необходимости
```

3. **Запуск через Docker Compose**
```bash
docker-compose up -d
```

4. **Проверка работоспособности**
```bash
curl http://localhost:8080/v1/healthcheck
```

### Доступные сервисы

- **API Server**: http://localhost:8080
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379
- **MongoDB**: localhost:27017
- **MinIO Console**: http://localhost:9001
- **MinIO API**: http://localhost:9000

## 📖 Документация

### Основные документы

- **[DOCUMENTATION.md](./DOCUMENTATION.md)** - Полная документация проекта
- **[API_ENDPOINTS.md](./API_ENDPOINTS.md)** - Описание всех API эндпоинтов
- **[DATABASE_SCHEMA.md](./DATABASE_SCHEMA.md)** - Схема базы данных
- **[ARCHITECTURE.md](./ARCHITECTURE.md)** - Архитектура системы

### Дополнительные ресурсы

- **[docs/MINIO_API.md](./docs/MINIO_API.md)** - API для работы с файлами

## 🏗️ Архитектура

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │   Mobile App    │    │   API Client    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      Nginx Proxy          │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │     Go API Server         │
                    │      (Port: 4000)         │
                    └─────────────┬─────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌───────┴────────┐    ┌─────────┴────────┐    ┌─────────┴────────┐
│   PostgreSQL   │    │      Redis       │    │     MongoDB      │
│   (Port: 5432) │    │   (Port: 6379)   │    │  (Port: 27017)   │
└────────────────┘    └──────────────────┘    └──────────────────┘
        │
┌───────┴────────┐
│     MinIO      │
│  (Port: 9000)  │
└────────────────┘
```

## 🔧 Основные компоненты

### Backend (Go)
- **HTTP Router**: julienschmidt/httprouter
- **Database**: PostgreSQL с lib/pq драйвером
- **Cache**: Redis с go-redis клиентом
- **Logging**: MongoDB для структурированных логов
- **Files**: MinIO для хранения аудио и изображений
- **Auth**: JWT токены с access/refresh парой

### Базы данных
- **PostgreSQL** - основные данные (пользователи, контент, прогресс)
- **Redis** - кэширование и сессии
- **MongoDB** - логи и аналитика
- **MinIO** - файловое хранилище

## 📊 Основные сущности

### Пользователи и аутентификация
- `User` - пользователи системы
- `AuthorizationToken` - JWT токены

### Образовательный контент
- `Word` - словарь (казахский ↔ русский)
- `Sentence` - примеры предложений
- `Question` - интерактивные задания
- `Theory` - теоретический материал
- `Module` - учебные модули

### Прогресс и достижения
- `Progress` - прогресс обучения пользователей
- `Achievement` - система достижений
- `UserAchievement` - достижения пользователей

## 🔐 Аутентификация

### Регистрация
```bash
curl -X POST http://localhost:8080/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Айдар",
    "surname": "Нурланов", 
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

### Вход в систему
```bash
curl -X POST http://localhost:8080/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

### Использование токена
```bash
curl -X GET http://localhost:8080/v1/module/all \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 📁 Работа с файлами

### Загрузка аудио
```bash
curl -X POST http://localhost:8080/v1/files/upload/audio \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "audio=@pronunciation.mp3"
```

### Загрузка изображения
```bash
curl -X POST http://localhost:8080/v1/files/upload/image \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "image=@lesson_image.jpg"
```

## 🛠️ Разработка

### Локальная разработка

1. **Установка зависимостей**
```bash
go mod download
```

2. **Запуск базы данных**
```bash
docker-compose up -d postgres redis mongo minio
```

3. **Миграции**
```bash
migrate -path ./migrations -database "postgres://olzzhas:Olzhas040404@localhost:5432/klingo_dev?sslmode=disable" up
```

4. **Запуск приложения**
```bash
go run ./cmd/api
```

### Тестирование

```bash
# Запуск всех тестов
go test ./...

# Тесты с покрытием
go test -cover ./...

# Тесты конкретного пакета
go test ./internal/data
```

### Миграции базы данных

```bash
# Создание новой миграции
migrate create -ext sql -dir migrations -seq create_new_table

# Применение миграций
migrate -path ./migrations -database $DB_DSN up

# Откат миграций
migrate -path ./migrations -database $DB_DSN down 1
```

## 🔍 Мониторинг

### Health Check
```bash
curl http://localhost:8080/v1/healthcheck
```

### Метрики
```bash
curl http://localhost:8080/debug/vars
```

### Логи
```bash
# Логи приложения
docker-compose logs app

# Логи базы данных
docker-compose logs postgres

# Все логи
docker-compose logs
```

## 🚀 Развертывание

### Production

1. **Настройка переменных окружения**
```bash
export DB_DSN="postgres://user:pass@host:port/db?sslmode=require"
export REDIS_URL="redis://host:port"
export MONGO_URL="************************:port/db"
# ... другие переменные
```

2. **Сборка Docker образа**
```bash
docker build -t kazakh-lingo:latest .
```

3. **Запуск с production конфигурацией**
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 📈 Производительность

### Рекомендации по оптимизации

1. **База данных**
   - Используйте connection pooling
   - Добавьте индексы для часто используемых запросов
   - Настройте партиционирование для больших таблиц

2. **Кэширование**
   - Кэшируйте часто запрашиваемые данные в Redis
   - Используйте CDN для статических файлов
   - Настройте HTTP кэширование

3. **API**
   - Реализуйте пагинацию для больших списков
   - Используйте сжатие gzip
   - Оптимизируйте JSON сериализацию

## 🤝 Вклад в проект

1. Fork репозитория
2. Создайте feature branch (`git checkout -b feature/amazing-feature`)
3. Commit изменения (`git commit -m 'Add amazing feature'`)
4. Push в branch (`git push origin feature/amazing-feature`)
5. Создайте Pull Request

## 📄 Лицензия

Этот проект лицензирован под MIT License - см. файл [LICENSE](LICENSE) для деталей.

## 👥 Команда

- **Backend Developer**: Разработка API и архитектуры
- **Database Designer**: Проектирование схемы базы данных
- **DevOps Engineer**: Настройка инфраструктуры и развертывания

## 📞 Поддержка

Если у вас есть вопросы или проблемы:

1. Проверьте [документацию](./DOCUMENTATION.md)
2. Посмотрите [Issues](../../issues) на GitHub
3. Создайте новый Issue с подробным описанием проблемы

---

**Kazakh-Lingo** - современная платформа для изучения казахского языка 🇰🇿
