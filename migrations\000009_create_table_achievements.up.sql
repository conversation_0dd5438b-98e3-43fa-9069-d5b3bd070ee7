-- Таблица достижений
CREATE TABLE IF NOT EXISTS achievements (
      id SERIAL PRIMARY KEY,
      name TEXT NOT NULL,              -- Название достижения
      description TEXT NOT NULL,       -- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
      type TEXT NOT NULL,              -- Тип (например, "progress", "friends", "streaks")
      target INT NOT NULL,             -- Цель (например, 7 дней, 10 друзей)
      created_at TIMESTAMP DEFAULT now(),
      updated_at TIMESTAMP DEFAULT now()
);

-- Таблица прогресса пользователей
CREATE TABLE  IF NOT EXISTS user_achievements (
       id SERIAL PRIMARY KEY,
       user_id INT NOT NULL,              -- Пользователь
       achievement_id INT NOT NULL,       -- Достижение
       progress INT NOT NULL DEFAULT 0,   -- Прогресс
       achieved BOOLEAN DEFAULT FALSE,    -- За<PERSON>ершено или нет
       created_at TIMESTAMP DEFAULT now(),
       updated_at TIMESTAMP DEFAULT now(),
       FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
       FOREIGN KEY (achievement_id) REFERENCES achievements(id) ON DELETE CASCADE
);
