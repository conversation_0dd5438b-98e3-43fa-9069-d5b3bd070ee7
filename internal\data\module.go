package data

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"github.com/go-redis/redis/v8"
	"github.com/lib/pq"
	"time"
)

type Module struct {
	ID              int         `json:"id"`
	Name            string      `json:"name"`
	TheoryIDs       []int64     `json:"theory_ids"`
	QuestionIDs     []int64     `json:"question_ids"`
	Questions       []*Question `json:"questions"`
	Theories        []*Theory   `json:"theories"`
	PreRequisiteIDs []int64     `json:"pre_requisite_ids"`
	Level           int         `json:"level"`
	CreatedAt       time.Time   `json:"created_at"`
}

type ModuleModel struct {
	DB    *sql.DB
	Redis *redis.Client
}

func (m *ModuleModel) Insert(module *Module) error {
	query := `
        INSERT INTO modules (name, theory_ids, question_ids, pre_requisite_ids, level, created_at)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, created_at
    `
	args := []interface{}{
		module.Name,
		pq.Array(module.TheoryIDs),
		pq.<PERSON>(module.QuestionIDs),
		pq.Array(module.PreRequisiteIDs), // Добавляем новое поле
		module.Level,
		time.Now(),
	}

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	err := m.DB.QueryRowContext(ctx, query, args...).Scan(&module.ID, &module.CreatedAt)
	if err != nil {
		return err
	}
	return nil
}

func (m *ModuleModel) Get(id int) (*Module, error) {
	query := `
        SELECT id, name, theory_ids, question_ids, pre_requisite_ids, level, created_at
        FROM modules
        WHERE id = $1
    `

	var module Module
	var theoryIDs, questionIDs, preRequisiteIDs pq.Int64Array

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	err := m.DB.QueryRowContext(ctx, query, id).Scan(
		&module.ID, &module.Name, &theoryIDs, &questionIDs, &preRequisiteIDs,
		&module.Level, &module.CreatedAt,
	)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}

	module.TheoryIDs = theoryIDs
	module.QuestionIDs = questionIDs
	module.PreRequisiteIDs = preRequisiteIDs

	return &module, nil
}

func (m *ModuleModel) GetAll() ([]*Module, error) {
	query := `
        SELECT id, name, theory_ids, question_ids, pre_requisite_ids, level, created_at
        FROM modules
    `

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	rows, err := m.DB.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var modules []*Module
	for rows.Next() {
		var module Module
		var theoryIDs, questionIDs, preRequisiteIDs pq.Int64Array

		err := rows.Scan(
			&module.ID, &module.Name, &theoryIDs, &questionIDs, &preRequisiteIDs,
			&module.Level, &module.CreatedAt,
		)
		if err != nil {
			return nil, err
		}

		module.TheoryIDs = []int64(theoryIDs)
		module.QuestionIDs = []int64(questionIDs)
		module.PreRequisiteIDs = []int64(preRequisiteIDs)

		modules = append(modules, &module)
	}
	if err = rows.Err(); err != nil {
		return nil, err
	}

	return modules, nil
}

func (m *ModuleModel) Update(module *Module) error {
	query := `
        UPDATE modules
        SET name = $1, theory_ids = $2, question_ids = $3, pre_requisite_ids = $4, level = $5
        WHERE id = $6
    `
	args := []interface{}{
		module.Name,
		pq.Array(module.TheoryIDs),
		pq.Array(module.QuestionIDs),
		pq.Array(module.PreRequisiteIDs), // обновляем pre_requisite_ids
		module.Level,
		module.ID,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	_, err := m.DB.ExecContext(ctx, query, args...)
	if err != nil {
		return err
	}
	return nil
}

func (m *ModuleModel) Delete(id int) error {
	query := `DELETE FROM modules WHERE id = $1`

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	_, err := m.DB.ExecContext(ctx, query, id)
	if err != nil {
		return err
	}
	return nil
}

func (m *ModuleModel) GetFullModuleById(id int64) (*Module, error) {
	query := `
        SELECT id, name, theory_ids, question_ids, pre_requisite_ids, level, created_at
        FROM modules
        WHERE id = $1
    `

	var module Module
	var theoryIDs, questionIDs, preRequisiteIDs pq.Int64Array

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	err := m.DB.QueryRowContext(ctx, query, id).Scan(
		&module.ID, &module.Name, &theoryIDs, &questionIDs, &preRequisiteIDs,
		&module.Level, &module.CreatedAt,
	)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}

	module.TheoryIDs = theoryIDs
	module.QuestionIDs = questionIDs
	module.PreRequisiteIDs = preRequisiteIDs

	theories, err := m.getTheoriesByIds(theoryIDs)
	if err != nil {
		return nil, err
	}

	questions, err := m.getQuestionsByIds(questionIDs)
	if err != nil {
		return nil, err
	}

	module.Theories = theories
	module.Questions = questions

	return &module, nil
}

// Вспомогательный метод для получения теорий по идентификаторам
func (m *ModuleModel) getTheoriesByIds(ids []int64) ([]*Theory, error) {
	query := `
        SELECT id, title, description, module_id, tags, examples_ids, created_at
        FROM theories
        WHERE id = ANY($1)
    `

	rows, err := m.DB.Query(query, pq.Array(ids))
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var theories []*Theory
	for rows.Next() {
		var theory Theory
		var examplesIDs pq.Int64Array

		err := rows.Scan(&theory.ID, &theory.Title, &theory.Description, &theory.ModuleID,
			pq.Array(&theory.Tags), &examplesIDs, &theory.CreatedAt)
		if err != nil {
			return nil, err
		}

		theory.ExamplesIDs = []int64(examplesIDs)
		theories = append(theories, &theory)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return theories, nil
}

// Вспомогательный метод для получения вопросов по идентификаторам
func (m *ModuleModel) getQuestionsByIds(ids []int64) ([]*Question, error) {
	query := `
		SELECT q.id, q.type, q.correct_answer, q.image_url, w.id, w.kaz_plaintext, w.rus_plaintext, w.audio_url, qw.sequence_order
		FROM questions q
		JOIN question_words qw ON q.id = qw.question_id
		JOIN words w ON qw.word_id = w.id
		WHERE q.id = ANY($1)
		ORDER BY q.id, qw.sequence_order
	`

	rows, err := m.DB.Query(query, pq.Array(ids))
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	questionsMap := make(map[int]*Question)
	var questions []*Question

	for rows.Next() {
		var questionID int
		var questionType, imageURL string
		var correctAnswerJSON []byte
		var word Word
		var sequenceOrder int

		err := rows.Scan(&questionID, &questionType, &correctAnswerJSON, &imageURL, &word.ID,
			&word.KazPlaintext, &word.RusPlaintext, &word.AudioURL, &sequenceOrder)
		if err != nil {
			return nil, err
		}

		if _, exists := questionsMap[questionID]; !exists {
			questionsMap[questionID] = &Question{
				ID:       questionID,
				Type:     questionType,
				ImageURL: imageURL,
				Words:    []Word{},
			}
			json.Unmarshal(correctAnswerJSON, &questionsMap[questionID].CorrectAnswer)

			questions = append(questions, questionsMap[questionID])
		}

		questionsMap[questionID].Words = append(questionsMap[questionID].Words, word)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return questions, nil
}

func (m *ModuleModel) GetUserPassedModules(userId int64) ([]int64, error) {
	query := `
		SELECT DISTINCT module_id FROM progress WHERE user_id = $1 ORDER BY module_id ASC
	`

	rows, err := m.DB.Query(query, userId) // Замените m.DB на ваш объект доступа к базе данных
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var moduleIds []int64
	for rows.Next() {
		var moduleId int64
		if err := rows.Scan(&moduleId); err != nil {
			return nil, err
		}
		moduleIds = append(moduleIds, moduleId)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return moduleIds, nil
}
