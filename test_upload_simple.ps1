# Simple test for MinIO file upload

Write-Host "Testing MinIO File Upload API..." -ForegroundColor Green

# Create test files
$testAudioContent = "This is a test audio file for MinIO"
$testAudioPath = "test_audio.mp3"
[System.IO.File]::WriteAllText($testAudioPath, $testAudioContent)

$testImageContent = "This is a test image file for MinIO"
$testImagePath = "test_image.jpg"
[System.IO.File]::WriteAllText($testImagePath, $testImageContent)

Write-Host "Created test files: $testAudioPath, $testImagePath" -ForegroundColor Yellow

# Test audio upload using curl (if available)
Write-Host "`nTesting audio upload..." -ForegroundColor Yellow
try {
    $curlResult = & curl.exe -X POST -F "audio=@$testAudioPath" http://localhost:8080/v1/files/upload/audio 2>&1
    Write-Host "Audio upload result: $curlResult" -ForegroundColor Green
} catch {
    Write-Host "Curl not available or failed: $($_.Exception.Message)" -ForegroundColor Red
    
    # Alternative method using Invoke-RestMethod
    Write-Host "Trying alternative method..." -ForegroundColor Yellow
    try {
        $boundary = [System.Guid]::NewGuid().ToString()
        $LF = "`r`n"
        
        $fileBytes = [System.IO.File]::ReadAllBytes($testAudioPath)
        $fileEnc = [System.Text.Encoding]::GetEncoding('iso-8859-1').GetString($fileBytes)
        
        $bodyLines = (
            "--$boundary",
            "Content-Disposition: form-data; name=`"audio`"; filename=`"$testAudioPath`"",
            "Content-Type: audio/mpeg$LF",
            $fileEnc,
            "--$boundary--$LF"
        ) -join $LF
        
        $response = Invoke-RestMethod -Uri "http://localhost:8080/v1/files/upload/audio" -Method Post -ContentType "multipart/form-data; boundary=$boundary" -Body $bodyLines
        Write-Host "Audio upload successful!" -ForegroundColor Green
        Write-Host ($response | ConvertTo-Json -Depth 3) -ForegroundColor Cyan
    } catch {
        Write-Host "Alternative method failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test list files
Write-Host "`nTesting list files..." -ForegroundColor Yellow
try {
    $listResponse = Invoke-RestMethod -Uri "http://localhost:8080/v1/files/list?bucket=klingo-audio" -Method GET
    Write-Host "List files successful!" -ForegroundColor Green
    Write-Host ($listResponse | ConvertTo-Json -Depth 3) -ForegroundColor Cyan
} catch {
    Write-Host "List files failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Cleanup
Write-Host "`nCleaning up test files..." -ForegroundColor Yellow
Remove-Item $testAudioPath -ErrorAction SilentlyContinue
Remove-Item $testImagePath -ErrorAction SilentlyContinue
Write-Host "Test completed!" -ForegroundColor Green
