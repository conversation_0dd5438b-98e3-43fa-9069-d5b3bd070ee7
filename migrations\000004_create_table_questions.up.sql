-- migrations/001_create_questions_and_words_tables.up.sql

CREATE TABLE IF NOT EXISTS words (
     id SERIAL PRIMARY KEY,
     kaz_plaintext TEXT NOT NULL,
     rus_plaintext TEXT NOT NULL,
     audio_url TEXT
);

CREATE TABLE IF NOT EXISTS questions (
     id SERIAL PRIMARY KEY,
     type TEXT NOT NULL,
     correct_answer JSONB,
     image_url TEXT,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS question_words (
    question_id INT REFERENCES questions(id) ON DELETE CASCADE,
    word_id INT REFERENCES words(id) ON DELETE CASCADE,
    sequence_order INT,
    PRIMARY KEY (question_id, word_id)
);
