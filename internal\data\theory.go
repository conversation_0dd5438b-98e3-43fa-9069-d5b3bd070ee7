package data

import (
	"context"
	"database/sql"
	"errors"
	"github.com/go-redis/redis/v8"
	"github.com/lib/pq"
	"time"
)

type Theory struct {
	ID          int         `json:"id"`
	Title       string      `json:"title"`
	Description string      `json:"description"`
	ModuleID    int         `json:"module_id"`
	Tags        []string    `json:"tags"`
	ExamplesIDs []int64     `json:"examples_ids"`
	Examples    []*Sentence `json:"examples"`
	CreatedAt   time.Time   `json:"created_at"`
}

type TheoryModel struct {
	DB    *sql.DB
	Redis *redis.Client
}

// Insert Вставка новой теории
func (m *TheoryModel) Insert(theory *Theory) error {
	query := `
        INSERT INTO theories (title, description, module_id, tags, examples_ids, created_at)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, created_at
    `
	args := []interface{}{
		theory.Title, theory.Description, theory.ModuleID,
		pq.Array(theory.Tags), pq.Array(theory.ExamplesIDs), time.Now(),
	}

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	err := m.DB.QueryRowContext(ctx, query, args...).Scan(&theory.ID, &theory.CreatedAt)
	if err != nil {
		return err
	}
	return nil
}

// Get Получение теории по ID
func (m *TheoryModel) Get(id int) (*Theory, error) {
	query := `
        SELECT id, title, description, module_id, tags, examples_ids, created_at
        FROM theories
        WHERE id = $1
    `

	var theory Theory
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	err := m.DB.QueryRowContext(ctx, query, id).Scan(
		&theory.ID, &theory.Title, &theory.Description, &theory.ModuleID,
		pq.Array(&theory.Tags), pq.Array(&theory.ExamplesIDs), &theory.CreatedAt,
	)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}
	return &theory, nil
}

// GetAll Получение всех теорий
func (m *TheoryModel) GetAll() ([]*Theory, error) {
	query := `
        SELECT 
            t.id, 
            t.title, 
            t.description, 
            t.module_id, 
            t.tags, 
            t.examples_ids, 
            t.created_at,
            s.id AS example_id, 
            s.kaz_plaintext, 
            s.rus_plaintext
        FROM theories t
        LEFT JOIN sentences s ON s.id = ANY(t.examples_ids)
        ORDER BY t.id, s.id
    `

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	rows, err := m.DB.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	theoryMap := make(map[int]*Theory) // Сохраняем теории с ID для агрегации примеров

	for rows.Next() {
		var theoryID int
		var theoryTitle, theoryDescription string
		var theoryModuleID int
		var theoryTags pq.StringArray
		var examplesIDs pq.Int64Array
		var createdAt time.Time

		var exampleID sql.NullInt64
		var exampleKaz sql.NullString
		var exampleRus sql.NullString

		err := rows.Scan(
			&theoryID, &theoryTitle, &theoryDescription, &theoryModuleID,
			&theoryTags, &examplesIDs, &createdAt,
			&exampleID, &exampleKaz, &exampleRus,
		)
		if err != nil {
			return nil, err
		}

		// Ищем теорию или создаём новую
		theory, exists := theoryMap[theoryID]
		if !exists {
			theory = &Theory{
				ID:          theoryID,
				Title:       theoryTitle,
				Description: theoryDescription,
				ModuleID:    theoryModuleID,
				Tags:        theoryTags,
				ExamplesIDs: examplesIDs,
				CreatedAt:   createdAt,
				Examples:    []*Sentence{}, // Инициализируем пустым массивом
			}
			theoryMap[theoryID] = theory
		}

		// Если пример существует, добавляем его
		if exampleID.Valid {
			theory.Examples = append(theory.Examples, &Sentence{
				ID:           int(exampleID.Int64),
				KazPlaintext: exampleKaz.String,
				RusPlaintext: exampleRus.String,
			})
		}
	}
	if err = rows.Err(); err != nil {
		return nil, err
	}

	// Конвертируем map в массив
	var theories []*Theory
	for _, theory := range theoryMap {
		theories = append(theories, theory)
	}

	return theories, nil
}

// Update Обновление теории
func (m *TheoryModel) Update(theory *Theory) error {
	query := `
        UPDATE theories
        SET title = $1, description = $2, module_id = $3, tags = $4, examples_ids = $5
        WHERE id = $6
    `
	args := []interface{}{
		theory.Title, theory.Description, theory.ModuleID,
		pq.Array(theory.Tags), pq.Array(theory.ExamplesIDs), theory.ID,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	_, err := m.DB.ExecContext(ctx, query, args...)
	if err != nil {
		return err
	}
	return nil
}

// Delete Удаление теории
func (m *TheoryModel) Delete(id int) error {
	query := `DELETE FROM theories WHERE id = $1`

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	_, err := m.DB.ExecContext(ctx, query, id)
	if err != nil {
		return err
	}
	return nil
}
