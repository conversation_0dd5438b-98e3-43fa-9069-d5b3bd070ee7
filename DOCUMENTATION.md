# Kazakh-Lingo - Детальная документация проекта

## Обзор проекта

Kazakh-Lingo - это веб-приложение для изучения казахского языка, построенное на архитектуре микросервисов с использованием Go, PostgreSQL, Redis, MongoDB и MinIO. Проект предоставляет API для управления пользователями, образовательным контентом, прогрессом обучения и файлами.

## Архитектура системы

### Основные компоненты:
- **API Server** (Go) - основной бэкенд на порту 4000
- **PostgreSQL** - основная база данных для структурированных данных
- **Redis** - кэширование и сессии
- **MongoDB** - логирование и аналитика
- **MinIO** - хранение файлов (аудио, изображения)
- **RabbitMQ** - очереди сообщений для логирования
- **Nginx** - обратный прокси и балансировка нагрузки

### Структура проекта:
```
kazakh-lingo/
├── cmd/api/           # Основное приложение и HTTP handlers
├── internal/
│   ├── data/          # Модели данных и бизнес-логика
│   ├── storage/       # Работа с файловым хранилищем
│   ├── image/         # Обработка изображений
│   ├── jsonlog/       # Структурированное логирование
│   └── validator/     # Валидация данных
├── migrations/        # SQL миграции базы данных
└── docs/             # Документация
```

## Модели данных

### 1. User (Пользователь)
**Назначение**: Управление пользователями системы

**Структура**:
```go
type User struct {
    ID        int       `json:"id"`
    Name      string    `json:"name"`
    Surname   string    `json:"surname"`
    Email     string    `json:"email"`
    Password  password  `json:"-"`
    ImageUrl  string    `json:"image_url"`
    Activated bool      `json:"activated"`
    CreatedAt time.Time `json:"created_at"`
}
```

**Методы**:
- `Insert(user *User)` - создание нового пользователя
- `Get(id int)` - получение пользователя по ID
- `GetByEmail(email string)` - получение пользователя по email
- `Update(user *User)` - обновление данных пользователя

**Валидация**:
- Имя и фамилия обязательны (до 500 символов)
- Email должен быть валидным и уникальным
- Пароль хэшируется с использованием bcrypt

### 2. AuthorizationToken (Токены авторизации)
**Назначение**: JWT токены для аутентификации и авторизации

**Структура**:
```go
type AuthorizationToken struct {
    ID           int64  `json:"id"`
    UserID       int64  `json:"user_id"`
    AccessToken  string `json:"access_token"`
    RefreshToken string `json:"refresh_token"`
}
```

**Методы**:
- `New(userID int64)` - создание новой пары токенов
- `DeleteAllForUser(userID int64)` - удаление всех токенов пользователя

### 3. Word (Слово)
**Назначение**: Базовые единицы языка для обучения

**Структура**:
```go
type Word struct {
    ID           int    `json:"id"`
    KazPlaintext string `json:"kaz_plaintext"`
    RusPlaintext string `json:"rus_plaintext"`
    AudioURL     string `json:"audio_url"`
}
```

**Методы**:
- `Insert(word *Word)` - создание нового слова
- `GetAll()` - получение всех слов

### 4. Sentence (Предложение)
**Назначение**: Примеры предложений для изучения контекста

**Структура**:
```go
type Sentence struct {
    ID           int    `json:"id"`
    KazPlaintext string `json:"kaz_plaintext"`
    RusPlaintext string `json:"rus_plaintext"`
    AudioURL     string `json:"audio_url"`
}
```

**Методы**:
- `Insert(sentence *Sentence)` - создание нового предложения
- `Get(id int)` - получение предложения по ID
- `GetAll()` - получение всех предложений

### 5. Question (Вопрос)
**Назначение**: Интерактивные задания для проверки знаний

**Структура**:
```go
type Question struct {
    ID            int     `json:"id"`
    Type          string  `json:"type"`
    Words         []*Word `json:"words"`
    CorrectAnswer string  `json:"correct_answer"`
    ImageURL      string  `json:"image_url"`
}
```

**Методы**:
- `Insert(question *Question)` - создание нового вопроса
- `Get(id int)` - получение вопроса с связанными словами
- `GetAll()` - получение всех вопросов

**Связи**: Связан с таблицей `words` через промежуточную таблицу `question_words`

### 6. Theory (Теория)
**Назначение**: Теоретический материал для изучения

**Структура**:
```go
type Theory struct {
    ID          int         `json:"id"`
    Title       string      `json:"title"`
    Description string      `json:"description"`
    ModuleID    int         `json:"module_id"`
    Tags        []string    `json:"tags"`
    ExampleIDs  []int64     `json:"example_ids"`
    Examples    []*Sentence `json:"examples"`
    CreatedAt   time.Time   `json:"created_at"`
}
```

**Методы**:
- `Insert(theory *Theory)` - создание новой теории
- `Get(id int)` - получение теории по ID
- `GetAll()` - получение всех теорий с примерами
- `Update(theory *Theory)` - обновление теории
- `Delete(id int)` - удаление теории

### 7. Module (Модуль)
**Назначение**: Группировка теорий и вопросов в учебные блоки

**Структура**:
```go
type Module struct {
    ID              int         `json:"id"`
    Name            string      `json:"name"`
    TheoryIDs       []int64     `json:"theory_ids"`
    QuestionIDs     []int64     `json:"question_ids"`
    Questions       []*Question `json:"questions"`
    Theories        []*Theory   `json:"theories"`
    PreRequisiteIDs []int64     `json:"pre_requisite_ids"`
    Level           int         `json:"level"`
    CreatedAt       time.Time   `json:"created_at"`
}
```

**Методы**:
- `Insert(module *Module)` - создание нового модуля
- `Get(id int)` - получение модуля по ID
- `GetAll()` - получение всех модулей
- `GetFullModuleById(id int64)` - получение модуля с полным контентом
- `Update(module *Module)` - обновление модуля
- `Delete(id int)` - удаление модуля

**Логика**: Модули имеют систему предварительных требований для последовательного обучения

### 8. Progress (Прогресс)
**Назначение**: Отслеживание прогресса пользователей

**Структура**:
```go
type Progress struct {
    ID                  int    `json:"id"`
    UserID              int    `json:"user_id"`
    ModuleID            int    `json:"module_id"`
    MistakenQuestionIds []int  `json:"mistaken_question_ids"`
    Time                string `json:"time"`
    TryCount            int    `json:"try_count"`
    CreatedAt           string `json:"created_at"`
    UpdatedAt           string `json:"updated_at"`
}
```

**Методы**:
- `SaveProgress(progress *Progress)` - сохранение прогресса
- `GetStreak(userID int64)` - расчет серии дней обучения
- `GetUserPassedModules(userID int64)` - получение пройденных модулей

### 9. Achievement (Достижения)
**Назначение**: Система мотивации через достижения

**Структуры**:
```go
type Achievement struct {
    ID          int       `json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    Type        string    `json:"type"`
    Target      int       `json:"target"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}

type UserAchievement struct {
    AchievementID int  `json:"achievement_id"`
    UserID        int  `json:"user_id"`
    Progress      int  `json:"progress"`
    Achieved      bool `json:"achieved"`
}
```

**Методы**:
- `GetUserAchievements(userID int)` - получение достижений пользователя
- `UnlockAchievement(userID int, achievementKey string)` - разблокировка достижения
- `SaveAchievement(achievement *Achievement)` - создание нового достижения

## API Эндпоинты

### Аутентификация
- `POST /v1/auth/register` - регистрация пользователя
- `POST /v1/auth/login` - вход в систему

### Пользователи
- Все эндпоинты требуют аутентификации через Bearer токен

### Вопросы
- `POST /v1/questions` - создание вопроса
- `GET /v1/questions/all` - получение всех вопросов
- `GET /v1/questions?id={id}` - получение вопроса по ID

### Слова
- `POST /v1/word` - создание слова
- `GET /v1/word` - получение всех слов

### Предложения
- `POST /v1/sentence` - создание предложения
- `GET /v1/sentence?id={id}` - получение предложения по ID
- `GET /v1/sentence/all` - получение всех предложений

### Теории
- `POST /v1/theory` - создание теории
- `GET /v1/theory?id={id}` - получение теории по ID
- `GET /v1/theory/all` - получение всех теорий
- `PUT /v1/theory` - обновление теории
- `DELETE /v1/theory?id={id}` - удаление теории

### Модули
- `POST /v1/module` - создание модуля
- `GET /v1/module/all` - получение всех модулей
- `GET /v1/module?id={id}` - получение полного модуля
- `PUT /v1/module` - обновление модуля
- `DELETE /v1/module?id={id}` - удаление модуля
- `GET /v1/module-user-progress/{id}` - прогресс пользователя по модулям

### Прогресс
- `POST /v1/progress/save` - сохранение прогресса
- `GET /v1/progress/streak/{id}` - получение серии дней обучения

### Достижения
- `GET /v1/achievements/{id}` - получение достижений пользователя
- `POST /v1/achievements` - создание достижения

### Файлы
- `POST /v1/files/upload/audio` - загрузка аудио файла
- `POST /v1/files/upload/image` - загрузка изображения
- `POST /v1/files/upload/multiple` - множественная загрузка файлов
- `DELETE /v1/files/delete?url={url}` - удаление файла
- `GET /v1/files/list?bucket={bucket}&prefix={prefix}` - список файлов

### Системные
- `GET /v1/healthcheck` - проверка состояния системы
- `GET /debug/vars` - метрики приложения

## Middleware и безопасность

### Middleware цепочка:
1. **metrics** - сбор метрик производительности
2. **recoverPanic** - восстановление после паники
3. **rateLimit** - ограничение частоты запросов
4. **authenticate** - аутентификация пользователей

### Аутентификация:
- JWT токены с access/refresh парой
- Access токен живет 15 минут
- Refresh токен живет 30 дней
- Токены хранятся в HTTP-only cookies

### Rate Limiting:
- Настраивается через конфигурацию
- По умолчанию: 2 запроса в секунду, burst до 4

## Файловое хранилище

### MinIO Storage:
- **Buckets**: `klingo-audio`, `klingo-images`
- **Поддерживаемые форматы аудио**: mp3, wav, ogg, m4a
- **Поддерживаемые форматы изображений**: jpg, jpeg, png, gif, webp
- **Ограничения**: аудио до 10MB, изображения до 5MB

### Google Cloud Storage:
- Используется для профильных изображений пользователей
- Интеграция через service account

## База данных

### PostgreSQL схема:
- **users** - пользователи
- **auth_tokens** - токены авторизации
- **words** - словарь
- **sentences** - предложения
- **questions** - вопросы
- **question_words** - связь вопросов и слов
- **theories** - теоретический материал
- **modules** - учебные модули
- **progress** - прогресс обучения
- **achievements** - достижения
- **user_achievements** - достижения пользователей

### Redis кэширование:
- Кэширование часто запрашиваемых данных
- Сессии пользователей
- Временные данные

### MongoDB логирование:
- Структурированные логи приложения
- Аналитические данные
- Аудит действий пользователей

## Конфигурация и развертывание

### Docker Compose:
- Полная инфраструктура в контейнерах
- Автоматические миграции базы данных
- Health checks для всех сервисов

### Переменные окружения:
- `DB_DSN` - строка подключения к PostgreSQL
- `REDIS_URL` - URL Redis
- `MONGO_URL` - URL MongoDB
- `MINIO_*` - настройки MinIO
- `ACCESS_SECRET`, `REFRESH_SECRET` - секреты для JWT

## Логирование и мониторинг

### Структурированное логирование:
- JSON формат логов
- Категории: general, auth, errors
- Интеграция с RabbitMQ для асинхронной обработки

### Метрики:
- Встроенные метрики Go (expvar)
- Время ответа HTTP запросов
- Количество активных соединений
- Использование памяти

## Валидация данных

### Система валидации:
- Централизованная валидация через `internal/validator`
- Валидация email адресов
- Проверка силы паролей
- Валидация входных данных API

## Обработка ошибок

### Типы ошибок:
- `ErrRecordNotFound` - запись не найдена
- `ErrEditConflict` - конфликт при редактировании
- `ErrDuplicateEmail` - дублирование email

### HTTP ответы:
- 400 Bad Request - неверные данные
- 401 Unauthorized - требуется аутентификация
- 403 Forbidden - недостаточно прав
- 404 Not Found - ресурс не найден
- 409 Conflict - конфликт данных
- 422 Unprocessable Entity - ошибки валидации
- 429 Too Many Requests - превышен лимит запросов
- 500 Internal Server Error - внутренняя ошибка сервера

## Взаимосвязи между компонентами

### Схема связей:
```
User (1) ←→ (N) Progress ←→ (1) Module
Module (1) ←→ (N) Theory
Module (1) ←→ (N) Question ←→ (N) Word
Theory (1) ←→ (N) Sentence (examples)
User (1) ←→ (N) UserAchievement ←→ (1) Achievement
User (1) ←→ (N) AuthorizationToken
```

### Бизнес-логика связей:

1. **User → Progress → Module**:
   - Пользователь проходит модули и сохраняет прогресс
   - Отслеживаются ошибочные ответы и время прохождения
   - Подсчитывается количество попыток

2. **Module → Theory + Question**:
   - Модуль содержит теоретический материал и практические задания
   - Теории предоставляют знания, вопросы проверяют их усвоение
   - Система предварительных требований обеспечивает последовательность обучения

3. **Question → Word**:
   - Вопросы состоят из слов в определенной последовательности
   - Поддерживаются различные типы вопросов (перевод, аудирование, составление предложений)

4. **Theory → Sentence**:
   - Теории содержат примеры предложений для демонстрации правил
   - Примеры помогают понять контекст использования

5. **User → Achievement**:
   - Система достижений мотивирует пользователей
   - Отслеживается прогресс по различным метрикам

## Недостающие компоненты и рекомендации

### Критически важные недостающие эндпоинты:

#### 1. Управление пользователями (Admin)
```
GET /v1/admin/users - список всех пользователей
PUT /v1/admin/users/{id} - редактирование пользователя
DELETE /v1/admin/users/{id} - удаление пользователя
GET /v1/admin/stats - статистика системы
```

#### 2. Профиль пользователя
```
GET /v1/profile - получение профиля текущего пользователя
PUT /v1/profile - обновление профиля
PUT /v1/profile/password - смена пароля
POST /v1/profile/avatar - загрузка аватара
```

#### 3. Поиск и фильтрация
```
GET /v1/search?q={query}&type={type} - поиск по контенту
GET /v1/modules?level={level}&completed={bool} - фильтрация модулей
GET /v1/questions?type={type}&difficulty={level} - фильтрация вопросов
```

#### 4. Статистика и аналитика
```
GET /v1/stats/user/{id} - детальная статистика пользователя
GET /v1/stats/module/{id} - статистика по модулю
GET /v1/leaderboard - таблица лидеров
```

#### 5. Социальные функции
```
POST /v1/friends/add - добавление друга
GET /v1/friends - список друзей
GET /v1/friends/progress - прогресс друзей
```

### Недостающие модели данных:

#### 1. UserSettings (Настройки пользователя)
```go
type UserSettings struct {
    ID                int    `json:"id"`
    UserID           int    `json:"user_id"`
    Language         string `json:"language"`
    NotificationsEnabled bool `json:"notifications_enabled"`
    DailyGoal        int    `json:"daily_goal"`
    Theme            string `json:"theme"`
}
```

#### 2. Category (Категории контента)
```go
type Category struct {
    ID          int    `json:"id"`
    Name        string `json:"name"`
    Description string `json:"description"`
    Color       string `json:"color"`
    Icon        string `json:"icon"`
}
```

#### 3. Difficulty (Уровни сложности)
```go
type Difficulty struct {
    ID    int    `json:"id"`
    Name  string `json:"name"`
    Level int    `json:"level"`
}
```

#### 4. UserSession (Сессии обучения)
```go
type UserSession struct {
    ID        int       `json:"id"`
    UserID    int       `json:"user_id"`
    StartTime time.Time `json:"start_time"`
    EndTime   time.Time `json:"end_time"`
    ModuleID  int       `json:"module_id"`
    Score     int       `json:"score"`
}
```

### Недостающая функциональность:

#### 1. Система уведомлений
- Push уведомления о напоминаниях
- Email уведомления о достижениях
- Уведомления о новом контенте

#### 2. Офлайн режим
- Кэширование контента для офлайн использования
- Синхронизация прогресса при подключении

#### 3. Адаптивные алгоритмы
- Персонализация сложности на основе прогресса
- Рекомендации контента
- Интервальное повторение

#### 4. Мультимедиа функции
- Запись и проверка произношения
- Интерактивные упражнения
- Видео контент

#### 5. Геймификация
- Система очков опыта (XP)
- Уровни пользователей
- Ежедневные задания
- Турниры и соревнования

### Рекомендации по улучшению архитектуры:

#### 1. Кэширование
- Добавить кэширование для часто запрашиваемых данных
- Реализовать cache-aside pattern
- Использовать Redis для сессий и временных данных

#### 2. Безопасность
- Добавить RBAC (Role-Based Access Control)
- Реализовать rate limiting по пользователям
- Добавить CSRF защиту
- Логирование всех действий пользователей

#### 3. Производительность
- Добавить пагинацию для всех списков
- Реализовать lazy loading для связанных данных
- Оптимизировать SQL запросы
- Добавить индексы в базу данных

#### 4. Мониторинг
- Интеграция с Prometheus/Grafana
- Алерты на критические ошибки
- Мониторинг производительности базы данных
- Трейсинг запросов

#### 5. Тестирование
- Unit тесты для всех моделей
- Integration тесты для API
- Load тесты для критических эндпоинтов
- E2E тесты для пользовательских сценариев

## Примеры использования API

### Регистрация и аутентификация:
```bash
# Регистрация
curl -X POST http://localhost:8080/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Айдар",
    "surname": "Нурланов",
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'

# Вход
curl -X POST http://localhost:8080/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

### Работа с модулями:
```bash
# Получение всех модулей
curl -X GET http://localhost:8080/v1/module/all \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Получение полного модуля
curl -X GET "http://localhost:8080/v1/module?id=1" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Сохранение прогресса:
```bash
curl -X POST http://localhost:8080/v1/progress/save \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 1,
    "module_id": 1,
    "mistaken_question_ids": [2, 5],
    "time": "00:05:30"
  }'
```

### Загрузка файлов:
```bash
# Загрузка аудио
curl -X POST http://localhost:8080/v1/files/upload/audio \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "audio=@pronunciation.mp3"

# Загрузка изображения
curl -X POST http://localhost:8080/v1/files/upload/image \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "image=@lesson_image.jpg"
```

## Заключение

Проект Kazakh-Lingo представляет собой хорошо структурированное приложение для изучения языка с современной архитектурой. Основные сильные стороны:

1. **Модульная архитектура** - четкое разделение ответственности
2. **Масштабируемость** - использование микросервисов и контейнеризация
3. **Безопасность** - JWT аутентификация и валидация данных
4. **Производительность** - кэширование и оптимизированные запросы
5. **Мониторинг** - структурированное логирование и метрики

Для полноценного продакшн-готового приложения рекомендуется реализовать указанные выше недостающие компоненты и улучшения.
