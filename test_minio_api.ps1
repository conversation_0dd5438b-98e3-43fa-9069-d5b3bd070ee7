# Test script for MinIO API endpoints

$baseUrl = "http://localhost:8080/v1"

Write-Host "Testing MinIO File Upload API..." -ForegroundColor Green

# Test 1: Health check
Write-Host "`n1. Testing health check..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/healthcheck" -Method GET
    Write-Host "Health check: OK" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "Health check failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: List files (should work even if buckets are empty)
Write-Host "`n2. Testing list files..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/files/list?bucket=klingo-audio" -Method GET
    Write-Host "List files: OK" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "List files failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Red
}

# Test 3: Create a test file and upload it
Write-Host "`n3. Creating test files..." -ForegroundColor Yellow

# Create test audio file
$testAudioContent = "This is a test audio file content for MinIO testing."
$testAudioPath = "test_audio.mp3"
[System.IO.File]::WriteAllText($testAudioPath, $testAudioContent)

# Create test image file (simple text file with image extension)
$testImageContent = "This is a test image file content for MinIO testing."
$testImagePath = "test_image.jpg"
[System.IO.File]::WriteAllText($testImagePath, $testImageContent)

Write-Host "Test files created: $testAudioPath, $testImagePath" -ForegroundColor Green

# Test 4: Upload audio file
Write-Host "`n4. Testing audio upload..." -ForegroundColor Yellow
try {
    $form = @{
        audio = Get-Item $testAudioPath
    }
    $response = Invoke-RestMethod -Uri "$baseUrl/files/upload/audio" -Method POST -Form $form
    Write-Host "Audio upload: OK" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
    $audioUrl = $response.file_url
} catch {
    Write-Host "Audio upload failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody" -ForegroundColor Red
    }
}

# Test 5: Upload image file
Write-Host "`n5. Testing image upload..." -ForegroundColor Yellow
try {
    $form = @{
        image = Get-Item $testImagePath
    }
    $response = Invoke-RestMethod -Uri "$baseUrl/files/upload/image" -Method POST -Form $form
    Write-Host "Image upload: OK" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
    $imageUrl = $response.file_url
} catch {
    Write-Host "Image upload failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody" -ForegroundColor Red
    }
}

# Test 6: List files again (should show uploaded files)
Write-Host "`n6. Testing list files after upload..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/files/list?bucket=klingo-audio" -Method GET
    Write-Host "List audio files: OK" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "List audio files failed: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/files/list?bucket=klingo-images" -Method GET
    Write-Host "List image files: OK" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "List image files failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: Delete uploaded files (if they were uploaded successfully)
if ($audioUrl) {
    Write-Host "`n7. Testing file deletion..." -ForegroundColor Yellow
    try {
        $deleteBody = @{ file_url = $audioUrl } | ConvertTo-Json
        $response = Invoke-RestMethod -Uri "$baseUrl/files/delete" -Method DELETE -Body $deleteBody -ContentType "application/json"
        Write-Host "File deletion: OK" -ForegroundColor Green
        Write-Host ($response | ConvertTo-Json -Depth 3)
    } catch {
        Write-Host "File deletion failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Cleanup test files
Write-Host "`n8. Cleaning up test files..." -ForegroundColor Yellow
Remove-Item $testAudioPath -ErrorAction SilentlyContinue
Remove-Item $testImagePath -ErrorAction SilentlyContinue
Write-Host "Test files cleaned up" -ForegroundColor Green

Write-Host "`nMinIO API testing completed!" -ForegroundColor Green
