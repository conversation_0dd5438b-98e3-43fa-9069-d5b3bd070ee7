package data

import (
	"cloud.google.com/go/storage"
	"github.com/olzzhas/kazakh-lingo/internal/image"
	minioStorage "github.com/olzzhas/kazakh-lingo/internal/storage"
)

type Storages struct {
	ProfileImage image.ProfileImageStorage
	MinIO        *minioStorage.MinIOStorage
}

func NewStorages(client *storage.Client, minioClient *minioStorage.MinIOStorage) Storages {
	return Storages{
		ProfileImage: image.ProfileImageStorage{Client: client},
		MinIO:        minioClient,
	}
}
