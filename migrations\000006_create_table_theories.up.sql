CREATE TABLE IF NOT EXISTS theories (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,                                -- Название теории
    description TEXT NOT NULL,                          -- Описание теории
    module_id INT,                                      -- Идентификатор модуля, к которому относится теория
    tags TEXT[],                                        -- Массив тегов, порядок важен
    examples_ids INT[],                                 -- Массив идентификаторов примеров (ссылается на таблицу sentences)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP      -- Время создания записи
);