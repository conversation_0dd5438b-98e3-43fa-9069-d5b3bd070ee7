# Используем официальный образ Golang в качестве базового
FROM golang:1.22-alpine

# Устанавливаем рабочую директорию внутри контейнера
WORKDIR /app

# Копируем go.mod и go.sum для кэширования зависимостей на этапе сборки
COPY go.mod go.sum ./

# Загружаем зависимости
RUN go mod download

# Копируем весь исходный код в контейнер
COPY . .

# Собираем приложение с именем sport-hub, устанавливаем права
RUN go build -o sport-hub ./cmd/api

# Экспонируем порт для приложения
EXPOSE 4000

# Команда запуска приложения
CMD ["./sport-hub"]