package data

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"github.com/go-redis/redis/v8"
	"github.com/olzzhas/kazakh-lingo/internal/validator"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.org/x/crypto/bcrypt"
	"log"
	"time"
)

var (
	ErrDuplicateEmail = errors.New("duplicate email")
)

var AnonymousUser = &User{}

func (u *User) IsAnonymous() bool {
	return u == AnonymousUser
}

type User struct {
	ID        int       `json:"id"`
	Name      string    `json:"name"`
	Surname   string    `json:"surname"`
	Email     string    `json:"email"`
	Password  password  `json:"-"`
	ImageUrl  string    `json:"image_url"`
	Activated bool      `json:"activated"`
	CreatedAt time.Time `json:"created_at"`
}

type password struct {
	plaintext *string
	Hash      []byte
}

type UserModel struct {
	DB    *sql.DB
	Redis *redis.Client
	Mongo *mongo.Client
}

func (p *password) Set(plaintextPassword string) error {
	log.Println("Setting password:", plaintextPassword)
	hash, err := bcrypt.GenerateFromPassword([]byte(plaintextPassword), 12)
	if err != nil {
		return err
	}

	p.plaintext = &plaintextPassword
	p.Hash = hash

	log.Println("Generated hash:", string(hash))

	return nil
}

func (p *password) Matches(plaintextPassword string) (bool, error) {
	if len(p.Hash) == 0 {
		return false, errors.New("password hash is empty or invalid")
	}

	err := bcrypt.CompareHashAndPassword(p.Hash, []byte(plaintextPassword))
	if err != nil {
		switch {
		case errors.Is(err, bcrypt.ErrMismatchedHashAndPassword):
			return false, nil
		default:
			return false, err
		}
	}

	return true, nil
}

func (p *password) UnmarshalJSON(data []byte) error {
	var aux struct {
		Hash string `json:"password_hash"`
	}
	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}
	p.Hash = []byte(aux.Hash)
	return nil
}

func (p *password) MarshalJSON() ([]byte, error) {
	return json.Marshal(struct {
		Hash string `json:"password_hash"`
	}{
		Hash: string(p.Hash),
	})
}

func ValidateEmail(v *validator.Validator, email string) {
	v.Check(email != "", "email", "must be provided")
	v.Check(validator.Matches(email, validator.EmailRX), "email", "must be a valid email address")
}

func ValidatePasswordPlaintext(v *validator.Validator, password string) {
	v.Check(password != "", "password", "must be provided")
	v.Check(len(password) >= 8, "password", "must be at least 8 bytes long")
	v.Check(len(password) <= 72, "password", "must not be more than 72 bytes long")
}

func ValidateUser(v *validator.Validator, user *User) {
	v.Check(user.Name != "", "name", "must be provided")
	v.Check(len(user.Name) <= 500, "name", "must not be more than 500 bytes long")

	v.Check(user.Surname != "", "surname", "must be provided")
	v.Check(len(user.Surname) <= 500, "surname", "must not be more than 500 bytes long")

	//v.Check(user.Role == "admin" || user.Role == "student" || user.Role == "teacher", "role", "invalid user role")

	ValidateEmail(v, user.Email)

	if user.Password.plaintext != nil {
		ValidatePasswordPlaintext(v, *user.Password.plaintext)
	}

	if user.Password.Hash == nil {
		panic("missing password hash for user")
	}
}

func (m *UserModel) GetByEmail(email string) (*User, error) {
	query := `
		SELECT id, created_at, name, surname, email, password_hash, activated, image_url
		FROM users
		WHERE email = $1
	`

	var user User

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	err := m.DB.QueryRowContext(ctx, query, email).Scan(
		&user.ID,
		&user.CreatedAt,
		&user.Name,
		&user.Surname,
		&user.Email,
		&user.Password.Hash,
		&user.Activated,
		&user.ImageUrl,
	)

	if err != nil {
		switch {
		case errors.Is(err, sql.ErrNoRows):
			return nil, ErrRecordNotFound
		default:
			return nil, err
		}
	}

	return &user, nil
}

func (m *UserModel) Get(id int) (*User, error) {
	return nil, nil
}

func (m *UserModel) Insert(user *User) error {
	query := `
		INSERT INTO users (name, surname, email, password_hash, activated, image_url)
		VALUES ($1, $2, $3, $4, $5, $6)
		RETURNING id, created_at
	`

	args := []any{user.Name, user.Surname, user.Email, user.Password.Hash, user.Activated, user.ImageUrl}

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	err := m.DB.QueryRowContext(ctx, query, args...).Scan(&user.ID, &user.CreatedAt)

	if err != nil {
		switch {
		case err.Error() == `pq: duplicate key value violates unique constraint "users_email_key"`:
			return ErrDuplicateEmail
		default:
			return err
		}
	}

	return nil
}
