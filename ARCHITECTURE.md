# Kazakh-Lingo - Архитектура системы

## Обзор архитектуры

Kazakh-Lingo построен на основе микросервисной архитектуры с использованием контейнеризации Docker. Система спроектирована для обеспечения масштабируемости, надежности и простоты развертывания.

## Архитектурная диаграмма

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Client]
        MOBILE[Mobile App]
        API_CLIENT[API Client]
    end

    subgraph "Load Balancer"
        NGINX[Nginx Proxy]
    end

    subgraph "Application Layer"
        API[Go API Server<br/>Port: 4000]
    end

    subgraph "Data Layer"
        POSTGRES[(PostgreSQL<br/>Port: 5432)]
        REDIS[(Redis<br/>Port: 6379)]
        MONGO[(MongoDB<br/>Port: 27017)]
        MINIO[(MinIO<br/>Port: 9000)]
    end

    subgraph "Message Queue"
        RABBITMQ[RabbitMQ<br/>Port: 5672]
    end

    subgraph "External Services"
        GCS[Google Cloud Storage]
    end

    WEB --> NGINX
    MOBILE --> NGINX
    API_CLIENT --> NGINX
    
    NGINX --> API
    
    API --> POSTGRES
    API --> REDIS
    API --> MONGO
    API --> MINIO
    API --> RABBITMQ
    API --> GCS
```

## Компоненты системы

### 1. API Server (Go)

**Назначение**: Основной бэкенд сервис, обрабатывающий все HTTP запросы

**Технологии**:
- Go 1.22
- HTTP Router (julienschmidt/httprouter)
- PostgreSQL driver (lib/pq)
- Redis client (go-redis/redis/v8)
- MongoDB driver (go.mongodb.org/mongo-driver)
- JWT authentication (dgrijalva/jwt-go)

**Ключевые особенности**:
- RESTful API
- JWT аутентификация
- Middleware для логирования, rate limiting, CORS
- Структурированное логирование
- Graceful shutdown
- Health checks

**Структура кода**:
```
cmd/api/
├── main.go           # Точка входа приложения
├── routes.go         # Определение маршрутов
├── middleware.go     # HTTP middleware
├── handlers/         # HTTP обработчики
├── errors.go         # Обработка ошибок
└── helpers.go        # Вспомогательные функции

internal/
├── data/            # Модели данных и бизнес-логика
├── storage/         # Работа с файловым хранилищем
├── validator/       # Валидация данных
└── jsonlog/         # Структурированное логирование
```

### 2. PostgreSQL Database

**Назначение**: Основная реляционная база данных для структурированных данных

**Конфигурация**:
- Версия: PostgreSQL 14
- Порт: 5432
- База данных: klingo_dev
- Пользователь: olzzhas

**Особенности**:
- ACID транзакции
- Полнотекстовый поиск
- JSON поддержка для массивов
- Автоматические миграции
- Connection pooling

**Хранимые данные**:
- Пользователи и аутентификация
- Образовательный контент (слова, предложения, вопросы)
- Модули и теории
- Прогресс обучения
- Система достижений

### 3. Redis Cache

**Назначение**: Кэширование и хранение сессий

**Конфигурация**:
- Версия: Redis Alpine
- Порт: 6379
- Persistent storage: включен

**Использование**:
- Кэширование часто запрашиваемых данных
- Сессии пользователей
- Rate limiting данные
- Временные токены

**Стратегии кэширования**:
- Cache-aside pattern
- TTL для автоматической очистки
- Namespace для разделения данных

### 4. MongoDB

**Назначение**: Хранение логов и аналитических данных

**Конфигурация**:
- Версия: MongoDB 5
- Порт: 27017
- Пользователь: olzzhas

**Коллекции**:
- `logs` - структурированные логи приложения
- `analytics` - данные для аналитики
- `audit` - аудит действий пользователей

**Особенности**:
- Схемы документов
- Индексы для быстрого поиска
- Агрегационные запросы
- Репликация для надежности

### 5. MinIO Object Storage

**Назначение**: Хранение файлов (аудио, изображения)

**Конфигурация**:
- Порт API: 9000
- Порт Console: 9001
- Access Key: olzzhas
- Secret Key: Olzhas040404

**Buckets**:
- `klingo-audio` - аудио файлы (mp3, wav, ogg, m4a)
- `klingo-images` - изображения (jpg, png, gif, webp)

**Особенности**:
- S3-совместимый API
- Публичные политики для чтения
- Автоматическое создание buckets
- Presigned URLs для приватных файлов

### 6. Google Cloud Storage

**Назначение**: Хранение профильных изображений пользователей

**Интеграция**:
- Service Account аутентификация
- Bucket для профильных изображений
- CDN для быстрой доставки контента

### 7. RabbitMQ

**Назначение**: Асинхронная обработка сообщений

**Использование**:
- Асинхронное логирование
- Обработка уведомлений
- Фоновые задачи

**Очереди**:
- `logs` - логи приложения
- `notifications` - уведомления пользователям
- `analytics` - аналитические события

### 8. Nginx Reverse Proxy

**Назначение**: Обратный прокси и балансировщик нагрузки

**Функции**:
- SSL терминация
- Сжатие gzip
- Статические файлы
- Rate limiting
- Load balancing

## Паттерны проектирования

### 1. Repository Pattern

Используется для абстракции доступа к данным:

```go
type UserModel struct {
    DB    *sql.DB
    Redis *redis.Client
    Mongo *mongo.Client
}

func (m *UserModel) Insert(user *User) error { ... }
func (m *UserModel) Get(id int) (*User, error) { ... }
func (m *UserModel) Update(user *User) error { ... }
```

### 2. Dependency Injection

Зависимости внедряются через структуру application:

```go
type application struct {
    config   config
    logger   *jsonlog.Logger
    models   data.Models
    storages data.Storages
    redis    *redis.Client
}
```

### 3. Middleware Chain

HTTP middleware организованы в цепочку:

```go
return app.metrics(
    app.recoverPanic(
        app.rateLimit(
            app.authenticate(router)
        )
    )
)
```

### 4. Factory Pattern

Для создания моделей и сервисов:

```go
func NewModels(db *sql.DB, redis *redis.Client, mongo *mongo.Client) Models {
    return Models{
        Users:     UserModel{DB: db, Redis: redis, Mongo: mongo},
        Questions: QuestionModel{DB: db, Redis: redis},
        // ...
    }
}
```

## Безопасность

### 1. Аутентификация и авторизация

- JWT токены с access/refresh парой
- Middleware для проверки токенов
- Role-based access control (планируется)

### 2. Защита данных

- Хэширование паролей (bcrypt)
- Prepared statements против SQL инъекций
- Валидация всех входных данных
- HTTPS для всех соединений

### 3. Rate Limiting

- Ограничение запросов по IP адресу
- Sliding window algorithm
- Настраиваемые лимиты

### 4. CORS

- Настроенные CORS политики
- Whitelist разрешенных доменов
- Preflight запросы

## Мониторинг и логирование

### 1. Структурированное логирование

```go
type Logger struct {
    out      io.Writer
    minLevel Level
    mu       sync.Mutex
}

func (l *Logger) PrintInfo(message string, properties map[string]any, category string)
func (l *Logger) PrintError(err error, properties map[string]any, category string)
```

### 2. Метрики

- HTTP метрики (время ответа, коды статуса)
- Системные метрики (память, CPU)
- Бизнес метрики (активные пользователи, прогресс)

### 3. Health Checks

```go
func (app *application) healthcheckHandler(w http.ResponseWriter, r *http.Request) {
    env := envelope{
        "status": "available",
        "system_info": map[string]string{
            "environment": app.config.env,
            "version":     version,
        },
    }
}
```

## Развертывание

### 1. Docker Compose

Полная инфраструктура описана в docker-compose.yaml:

```yaml
services:
  postgres:    # База данных
  redis:       # Кэш
  mongo:       # Логи
  minio:       # Файлы
  rabbitmq:    # Очереди
  migrate:     # Миграции
  app:         # API сервер
```

### 2. Environment Variables

Конфигурация через переменные окружения:

```bash
DB_DSN=postgres://user:pass@host:port/db
REDIS_URL=redis://host:port
MONGO_URL=************************:port/db
MINIO_ENDPOINT=host:port
ACCESS_SECRET=secret_key
REFRESH_SECRET=secret_key
```

### 3. Graceful Shutdown

```go
srv := &http.Server{
    Addr:         fmt.Sprintf(":%d", cfg.port),
    Handler:      app.routes(),
    IdleTimeout:  time.Minute,
    ReadTimeout:  10 * time.Second,
    WriteTimeout: 30 * time.Second,
}

// Graceful shutdown
quit := make(chan os.Signal, 1)
signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
<-quit

ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
defer cancel()

err = srv.Shutdown(ctx)
```

## Масштабирование

### 1. Горизонтальное масштабирование

- Stateless API серверы
- Load balancing через Nginx
- Database connection pooling
- Redis clustering

### 2. Вертикальное масштабирование

- Оптимизация SQL запросов
- Индексы базы данных
- Connection pooling
- Кэширование

### 3. Кэширование

- Redis для часто используемых данных
- Application-level кэширование
- CDN для статических файлов
- Database query caching

## Рекомендации по улучшению

### 1. Микросервисы

Разделение на отдельные сервисы:
- User Service
- Content Service
- Progress Service
- File Service
- Notification Service

### 2. Event-Driven Architecture

- Event sourcing для аудита
- CQRS для разделения чтения/записи
- Saga pattern для распределенных транзакций

### 3. Observability

- Distributed tracing (Jaeger)
- Metrics collection (Prometheus)
- Log aggregation (ELK Stack)
- APM (Application Performance Monitoring)

### 4. CI/CD Pipeline

- Automated testing
- Docker image building
- Kubernetes deployment
- Blue-green deployments
